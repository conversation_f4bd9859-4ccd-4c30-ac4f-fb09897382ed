package com.whiskerguard.general.notification.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.whiskerguard.general.config.ApplicationProperties;
import com.whiskerguard.general.model.*;
import com.whiskerguard.general.service.EmailService;
import com.whiskerguard.general.service.NotificationService;
import com.whiskerguard.general.service.PushService;
import com.whiskerguard.general.service.SmsService;
import com.whiskerguard.general.service.impl.NotificationServiceImpl;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

/**
 * 通知服务单元测试
 */
class NotificationServiceTest {

    @Mock
    private ApplicationProperties applicationProperties;

    @Mock
    private ApplicationProperties.Notification notification;

    @Mock
    private ApplicationProperties.Notification.Sms smsProperties;

    @Mock
    private EmailService emailService;

    @Mock
    private PushService pushService;

    @Mock
    private SmsService aliyunSmsService;

    @Mock
    private SmsService tencentSmsService;

    private NotificationService notificationService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        when(applicationProperties.getNotification()).thenReturn(notification);
        when(notification.getSms()).thenReturn(smsProperties);
        when(smsProperties.getDefaultProvider()).thenReturn(SmsProviderType.ALIYUN);

        notificationService = new NotificationServiceImpl(
            applicationProperties,
            emailService,
            pushService,
            aliyunSmsService,
            tencentSmsService
        );
    }

    @Test
    void testSendSmsWithAliyun() {
        // 准备测试数据
        SmsRequest request = new SmsRequest();
        request.setRecipient("13800138000");
        request.setTemplateId("SMS_123456");
        Map<String, Object> params = new HashMap<>();
        params.put("code", "123456");
        request.setTemplateParams(params);
        request.setProviderType(SmsProviderType.ALIYUN);

        NotificationResponse expectedResponse = NotificationResponse.success("短信发送成功");
        when(aliyunSmsService.send(any(SmsRequest.class))).thenReturn(expectedResponse);

        // 执行测试
        NotificationResponse response = notificationService.sendSms(request);

        // 验证结果
        verify(aliyunSmsService, times(1)).send(request);
        verify(tencentSmsService, never()).send(any(SmsRequest.class));
        assertEquals(expectedResponse, response);
    }

    @Test
    void testSendSmsWithTencent() {
        // 准备测试数据
        SmsRequest request = new SmsRequest();
        request.setRecipient("13800138000");
        request.setTemplateId("1234567");
        Map<String, Object> params = new HashMap<>();
        params.put("code", "123456");
        request.setTemplateParams(params);
        request.setProviderType(SmsProviderType.TENCENT);

        NotificationResponse expectedResponse = NotificationResponse.success("短信发送成功");
        when(tencentSmsService.send(any(SmsRequest.class))).thenReturn(expectedResponse);

        // 执行测试
        NotificationResponse response = notificationService.sendSms(request);

        // 验证结果
        verify(tencentSmsService, times(1)).send(request);
        verify(aliyunSmsService, never()).send(any(SmsRequest.class));
        assertEquals(expectedResponse, response);
    }

    @Test
    void testSendEmail() {
        // 准备测试数据
        EmailRequest request = new EmailRequest();
        request.setRecipient("<EMAIL>");
        request.setSubject("测试邮件");
        request.setContent("这是一封测试邮件");

        NotificationResponse expectedResponse = NotificationResponse.success("邮件发送成功");
        when(emailService.send(any(EmailRequest.class))).thenReturn(expectedResponse);

        // 执行测试
        NotificationResponse response = notificationService.sendEmail(request);

        // 验证结果
        verify(emailService, times(1)).send(request);
        assertEquals(expectedResponse, response);
    }

    @Test
    void testSendPush() {
        // 准备测试数据
        PushRequest request = new PushRequest();
        request.setTitle("测试推送");
        request.setContent("这是一条测试推送");
        request.getTargets().add("user123");

        NotificationResponse expectedResponse = NotificationResponse.success("推送发送成功");
        when(pushService.send(any(PushRequest.class))).thenReturn(expectedResponse);

        // 执行测试
        NotificationResponse response = notificationService.sendPush(request);

        // 验证结果
        verify(pushService, times(1)).send(request);
        assertEquals(expectedResponse, response);
    }
}
