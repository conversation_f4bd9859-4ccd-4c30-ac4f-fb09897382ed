# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

logging:
  level:
    ROOT: DEBUG
    tech.jhipster: DEBUG
    org.hibernate.SQL: DEBUG
    com.whiskerguard.general: DEBUG

management:
  zipkin: # Use the "zipkin" Maven profile to have the Spring Cloud Zipkin dependencies
    tracing:
      endpoint: http://localhost:9411/api/v2/spans
  tracing:
    sampling:
      probability: 1.0 # report 100% of traces

spring:
  devtools:
    restart:
      enabled: true
      additional-exclude: static/**
    livereload:
      enabled: false # we use Webpack dev server + BrowserSync for livereload
  jackson:
    serialization:
      indent-output: true
  cloud:
    consul:
      config:
        fail-fast: false # if not in "prod" profile, do not force to use Spring Cloud Config
        format: yaml
        profile-separator: '-'
      discovery:
        prefer-ip-address: true
        tags:
          - profile=${spring.profiles.active}
          - version='@project.version@'
          - git-version=${git.commit.id.describe:}
          - git-commit=${git.commit.id.abbrev:}
          - git-branch=${git.branch:}
      host: localhost
      port: 8500
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: ********************************************************************************************************************************************************
    username: root
    password:
    hikari:
      poolName: Hikari
      auto-commit: false
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
  liquibase:
    # Remove 'faker' if you do not want the sample data to be loaded automatically
    contexts: dev, faker
  messages:
    cache-duration: PT1S # 1 second, see the ISO 8601 standard
  thymeleaf:
    cache: false
  # Elasticsearch 配置
  elasticsearch:
    uris: http://***************:9200 #真实的es地址
    username: elastic
    password: Ye29WTuf
    connection-timeout: 1s
    socket-timeout: 30s

server:
  port: 8187
  # make sure requests the proxy uri instead of the server one
  forward-headers-strategy: native

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
  cache: # Cache configuration
    redis: # Redis configuration
      expiration: 3600 # By default objects stay 1 hour (in seconds) in the cache
      server: redis://localhost:6379
      cluster: false
      # server: redis://localhost:6379,redis://localhost:16379,redis://localhost:26379
      # cluster: true
  # CORS is disabled by default on microservice, as you should access them through a gateway.
  # If you want to enable it, please uncomment the configuration below.
  # cors:
  #   allowed-origins: "http://localhost:9000,https://localhost:9000"
  #   allowed-methods: "*"
  #   allowed-headers: "*"
  #   exposed-headers: "Authorization,Link,X-Total-Count"
  #   allow-credentials: true
  #   max-age: 1800
  security:
    authentication:
      jwt:
        # This token must be encoded using Base64 and be at least 256 bits long (you can type `openssl rand -base64 64` on your command line to generate a 512 bits one)
        base64-secret: NDNhOWIwYzNmOGYyMzMwZTQzNDI2MjIxMjZmYzkyZTJmOTcxNjY1ZWYwYWM5MTc1YjE4NWU3MTQ1NWI0M2NkZjBkYjcwZjM4MzE5NDVjYzAzMTBjMjJiOGFlNzZhZDRhN2JlZjM2MmNjNDhlMjYwNzZiYTBlOTY2OGU5MjdkMWY=
        # Token is valid 24 hours
        token-validity-in-seconds: 86400
        token-validity-in-seconds-for-remember-me: 2592000
  logging:
    use-json-format: false # By default, logs are not in Json format
    logstash: # Forward logs to logstash over a socket, used by LoggingConfiguration
      enabled: false
      host: localhost
      port: 5000
      ring-buffer-size: 512
# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================

tianyancha:
  api-token: 'b8fd5355-a31d-4bb7-9721-ee9530f213f7' # 替换为您的天眼查 API Token
  base-url: 'https://open.api.tianyancha.com/services/open/ic/'
  cache-expiration-hours: 168 # 缓存过期时间（小时），默认7天
  connection-timeout-ms: 5000 # 连接超时时间（毫秒）
  read-timeout-ms: 10000 # 读取超时时间（毫秒）
  max-retry-attempts: 3 # 最大重试次数
  retry-delay-ms: 1000 # 重试延迟时间（毫秒）

application:
  notification:
    email:
      enabled: false # 禁用邮件服务
      from: <EMAIL>
