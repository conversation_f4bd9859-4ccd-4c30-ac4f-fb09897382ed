<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity CompanyContact.
    -->
    <changeSet id="20250610000002-1" author="manus">
        <createTable tableName="company_contact">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="phone_number" type="varchar(100)">
                <constraints nullable="true" />
            </column>
            <column name="email" type="varchar(200)">
                <constraints nullable="true" />
            </column>
            <column name="website" type="varchar(500)">
                <constraints nullable="true" />
            </column>
            <column name="address" type="varchar(1000)">
                <constraints nullable="true" />
            </column>
            <column name="additional_details" type="${clobType}">
                <constraints nullable="true" />
            </column>
            <column name="company_id" type="bigint">
                <constraints nullable="true" unique="true" />
            </column>
        </createTable>
    </changeSet>

    <changeSet id="20250610000002-2" author="manus">
        <addForeignKeyConstraint baseColumnNames="company_id"
                                 baseTableName="company_contact"
                                 constraintName="fk_company_contact_company_id"
                                 referencedColumnNames="id"
                                 referencedTableName="company"/>
    </changeSet>

</databaseChangeLog>

