<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity CompanyRisk.
    -->
    <changeSet id="20250610000003-1" author="manus">
        <createTable tableName="company_risk">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="risk_type" type="varchar(100)">
                <constraints nullable="true" />
            </column>
            <column name="risk_description" type="varchar(1000)">
                <constraints nullable="true" />
            </column>
            <column name="risk_date" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="risk_level" type="varchar(50)">
                <constraints nullable="true" />
            </column>
            <column name="details" type="${clobType}">
                <constraints nullable="true" />
            </column>
            <column name="company_id" type="bigint">
                <constraints nullable="true" />
            </column>
        </createTable>
    </changeSet>

    <changeSet id="20250610000003-2" author="manus">
        <addForeignKeyConstraint baseColumnNames="company_id"
                                 baseTableName="company_risk"
                                 constraintName="fk_company_risk_company_id"
                                 referencedColumnNames="id"
                                 referencedTableName="company"/>
        <createIndex indexName="idx_company_risk_company_id" tableName="company_risk">
            <column name="company_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>

