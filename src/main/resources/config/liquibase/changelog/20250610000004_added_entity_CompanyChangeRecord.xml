<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity CompanyChangeRecord.
    -->
    <changeSet id="20250610000004-1" author="manus">
        <createTable tableName="company_change_record">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="change_item" type="varchar(200)">
                <constraints nullable="true" />
            </column>
            <column name="before_content" type="${clobType}">
                <constraints nullable="true" />
            </column>
            <column name="after_content" type="${clobType}">
                <constraints nullable="true" />
            </column>
            <column name="change_date" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="additional_details" type="${clobType}">
                <constraints nullable="true" />
            </column>
            <column name="company_id" type="bigint">
                <constraints nullable="true" />
            </column>
        </createTable>
    </changeSet>

    <changeSet id="20250610000004-2" author="manus">
        <addForeignKeyConstraint baseColumnNames="company_id"
                                 baseTableName="company_change_record"
                                 constraintName="fk_company_change_record_company_id"
                                 referencedColumnNames="id"
                                 referencedTableName="company"/>
        <createIndex indexName="idx_company_change_record_company_id" tableName="company_change_record">
            <column name="company_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>

