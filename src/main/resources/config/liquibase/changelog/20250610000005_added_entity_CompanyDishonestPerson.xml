<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity CompanyDishonestPerson.
    -->
    <changeSet id="20250610000005-1" author="manus">
        <createTable tableName="company_dishonest_person">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="person_name" type="varchar(200)">
                <constraints nullable="true" />
            </column>
            <column name="case_number" type="varchar(100)">
                <constraints nullable="true" />
            </column>
            <column name="publish_date" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="court_name" type="varchar(300)">
                <constraints nullable="true" />
            </column>
            <column name="execution_status" type="varchar(100)">
                <constraints nullable="true" />
            </column>
            <column name="details" type="${clobType}">
                <constraints nullable="true" />
            </column>
            <column name="company_id" type="bigint">
                <constraints nullable="true" />
            </column>
        </createTable>
    </changeSet>

    <changeSet id="20250610000005-2" author="manus">
        <addForeignKeyConstraint baseColumnNames="company_id"
                                 baseTableName="company_dishonest_person"
                                 constraintName="fk_company_dishonest_person_company_id"
                                 referencedColumnNames="id"
                                 referencedTableName="company"/>
        <createIndex indexName="idx_company_dishonest_person_company_id" tableName="company_dishonest_person">
            <column name="company_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>

