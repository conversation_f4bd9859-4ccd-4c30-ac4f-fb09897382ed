package com.whiskerguard.general.web.rest;

import com.whiskerguard.general.config.Constants;
import com.whiskerguard.general.service.FileUploadService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件上传相关
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/6
 */
@RestController
@RequestMapping("/api/file")
public class FileUploadResource {

    private static final Logger LOG = LoggerFactory.getLogger(FileUploadResource.class);

    private final FileUploadService fileUploadService;

    public FileUploadResource(FileUploadService fileUploadService) {
        this.fileUploadService = fileUploadService;
    }

    /**
     * 处理文件上传请求的方法。
     *
     * @param file         要上传的文件，通过请求参数 "file" 传递。
     * @param tenantId     租户ID，通过请求参数 "tenantId" 传递。
     * @param serviceName  服务名称，用以生成buket。
     * @param categoryName 以"-"组成的类别
     * @return 如果文件上传成功，返回包含文件URL的200 OK响应；
     * 如果文件为空，返回400 Bad Request响应；
     * 如果文件类型不被允许，返回400 Bad Request响应；
     * 如果上传过程中发生异常，返回500 Internal Server Error响应。
     */
    @PostMapping("/upload")
    public ResponseEntity<Map<String, Object>> uploadFile(
        @RequestParam(name = "file") MultipartFile file,
        @RequestParam(name = "tenantId") Long tenantId,
        @RequestParam(name = "serviceName") String serviceName,
        @RequestParam(name = "categoryName", required = false) String categoryName
    ) {
        LOG.info("Receiving file upload request for file: {}", file.getOriginalFilename());
        Map<String, Object> result;
        // 验证文件是否为空
        if (file.isEmpty()) {
            LOG.warn("Attempted to upload empty file");
            result = new HashMap<>();
            result.put("message", "文件不能为空");
            return ResponseEntity.badRequest().body(result);
        }

        // 验证文件类型
        String contentType = file.getContentType();
        if (contentType == null || !isAllowedFileType(contentType)) {
            LOG.warn("Invalid file type: {}", contentType);
            result = new HashMap<>();
            result.put("message", "不支持的文件类型");
            return ResponseEntity.badRequest().body(result);
        }

        try {
            result = fileUploadService.uploadFile(file, tenantId, categoryName, serviceName);
            LOG.info("Successfully uploaded file: {}", file.getOriginalFilename());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            LOG.error("Failed to upload file: {}", file.getOriginalFilename(), e);
            result = new HashMap<>();
            result.put("message", "文件上传失败" + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 获取指定目录下的文件列表。
     *
     * @param tenantId     租户ID，通过请求参数 "tenantId" 传递。
     * @param serviceName  服务名称，用以生成buket。
     * @param categoryName 以"-"组成的类别
     * @param uploadTime   上传文件的时间
     * @return 包含文件URL列表的200 OK响应；
     * 如果获取过程中发生异常，返回500 Internal Server Error响应。
     */
    @GetMapping("/list")
    public ResponseEntity<List<String>> getFilesInDirectory(
        @RequestParam(name = "tenantId") Long tenantId,
        @RequestParam(name = "serviceName") String serviceName,
        @RequestParam(name = "categoryName") String categoryName,
        @RequestParam(name = "uploadTime") String uploadTime
    ) {
        LOG.info(
            "Receiving file list request for tenantId: {}, categoryName: {}, uploadTime: {},serviceName:{}",
            tenantId,
            categoryName,
            uploadTime,
            serviceName
        );
        try {
            List<String> fileUrls = fileUploadService.getFilesInDirectory(tenantId, categoryName, uploadTime, serviceName);
            return ResponseEntity.ok(fileUrls);
        } catch (Exception e) {
            LOG.error(
                "Failed to retrieve file list for tenantId: {}, categoryName: {}, uploadTime: {},serviceName:{}",
                tenantId,
                categoryName,
                uploadTime,
                serviceName,
                e
            );
            return ResponseEntity.internalServerError().body(null);
        }
    }

    private boolean isAllowedFileType(String contentType) {
        for (String allowedType : Constants.ALLOWED_FILE_TYPES) {
            if (allowedType.equalsIgnoreCase(contentType)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取指定文件在COS中的URL。
     *
     * @param key 文件的Key
     * @return 包含文件URL的200 OK响应；
     * 如果获取过程中发生异常，返回500 Internal Server Error响应。
     */
    @GetMapping("/getFileUrl")
    public ResponseEntity<String> getFileUrl(@RequestParam(name = "key", required = false) String key) {
        LOG.info("Receiving file list request for key: {}", key);
        try {
            String fileUrl = fileUploadService.getFileUrl(key);
            return ResponseEntity.ok(fileUrl);
        } catch (Exception e) {
            LOG.error("Failed to retrieve file list for key: {}", key, e);
            return ResponseEntity.internalServerError().body(null);
        }
    }
}
