package com.whiskerguard.general.web.rest;

import com.whiskerguard.general.model.EmailRequest;
import com.whiskerguard.general.model.NotificationResponse;
import com.whiskerguard.general.model.PushRequest;
import com.whiskerguard.general.model.SmsRequest;
import com.whiskerguard.general.service.NotificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 通知服务管理
 */
@RestController
@RequestMapping("/api/notifications")
@Tag(name = "通知服务", description = "通知服务 API")
public class NotificationResource {

    private static final Logger log = LoggerFactory.getLogger(NotificationResource.class);

    private final NotificationService notificationService;

    @Autowired
    public NotificationResource(NotificationService notificationService) {
        this.notificationService = notificationService;
    }

    /**
     * 发送短信通知
     *
     * @param request 短信请求
     * @return 通知响应
     */
    @PostMapping("/sms")
    @Operation(summary = "发送短信通知", description = "发送短信通知到指定手机号")
    public ResponseEntity<NotificationResponse> sendSms(@RequestBody SmsRequest request) {
        log.debug("REST 请求发送短信通知: {}", request.getRecipient());
        NotificationResponse response = notificationService.sendSms(request);
        return ResponseEntity.ok(response);
    }

    /**
     * 发送邮件通知
     *
     * @param request 邮件请求
     * @return 通知响应
     */
    @PostMapping("/email")
    @Operation(summary = "发送邮件通知", description = "发送邮件通知到指定邮箱")
    public ResponseEntity<NotificationResponse> sendEmail(@RequestBody EmailRequest request) {
        log.debug("REST 请求发送邮件通知: {}", request.getRecipient());
        NotificationResponse response = notificationService.sendEmail(request);
        return ResponseEntity.ok(response);
    }

    /**
     * 发送推送通知
     *
     * @param request 推送请求
     * @return 通知响应
     */
    @PostMapping("/push")
    @Operation(summary = "发送推送通知", description = "发送推送通知到指定设备或用户")
    public ResponseEntity<NotificationResponse> sendPush(@RequestBody PushRequest request) {
        log.debug("REST 请求发送推送通知: {}", request.getTargets());
        NotificationResponse response = notificationService.sendPush(request);
        return ResponseEntity.ok(response);
    }
}
