package com.whiskerguard.general.web.rest;

import com.whiskerguard.general.repository.SensitiveWordRepository;
import com.whiskerguard.general.service.SensitiveWordService;
import com.whiskerguard.general.service.dto.SensitiveWordDTO;
import com.whiskerguard.general.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * 敏感词管理
 *
 * REST controller for managing {@link com.whiskerguard.general.domain.SensitiveWord}.
 */
@RestController
@RequestMapping("/api/sensitive-words")
public class SensitiveWordResource {

    private static final Logger LOG = LoggerFactory.getLogger(SensitiveWordResource.class);

    private static final String ENTITY_NAME = "whiskerguardGeneralServiceSensitiveWord";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final SensitiveWordService sensitiveWordService;

    private final SensitiveWordRepository sensitiveWordRepository;

    /**
     * 构造函数，通过依赖注入初始化服务
     */
    public SensitiveWordResource(SensitiveWordService sensitiveWordService, SensitiveWordRepository sensitiveWordRepository) {
        this.sensitiveWordService = sensitiveWordService;
        this.sensitiveWordRepository = sensitiveWordRepository;
    }

    /**
     * {@code POST  /sensitive-words} : 创建新的敏感词。
     *
     * @param sensitiveWordDTO 要创建的敏感词DTO
     * @return {@link ResponseEntity} 状态码 {@code 201 (Created)} 并在响应体中包含新创建的敏感词DTO，
     *         如果敏感词已存在ID则返回状态码 {@code 400 (Bad Request)}。
     * @throws URISyntaxException 如果位置URI语法不正确
     */
    @PostMapping("")
    public ResponseEntity<SensitiveWordDTO> createSensitiveWord(@Valid @RequestBody SensitiveWordDTO sensitiveWordDTO)
        throws URISyntaxException {
        LOG.debug("REST请求创建敏感词 : {}", sensitiveWordDTO);
        if (sensitiveWordDTO.getId() != null) {
            throw new BadRequestAlertException("新建敏感词不能指定ID", ENTITY_NAME, "idexists");
        }
        sensitiveWordDTO = sensitiveWordService.save(sensitiveWordDTO);
        return ResponseEntity.created(new URI("/api/sensitive-words/" + sensitiveWordDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, sensitiveWordDTO.getId().toString()))
            .body(sensitiveWordDTO);
    }

    /**
     * {@code PUT  /sensitive-words/:id} : 更新已存在的敏感词。
     *
     * @param id 要保存的敏感词DTO的ID
     * @param sensitiveWordDTO 要更新的敏感词DTO
     * @return {@link ResponseEntity} 状态码 {@code 200 (OK)} 并在响应体中包含更新后的敏感词DTO,
     *         如果敏感词DTO无效则返回状态码 {@code 400 (Bad Request)},
     *         如果敏感词DTO无法更新则返回状态码 {@code 500 (Internal Server Error)}
     * @throws URISyntaxException 如果位置URI语法不正确
     */
    @PutMapping("/{id}")
    public ResponseEntity<SensitiveWordDTO> updateSensitiveWord(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody SensitiveWordDTO sensitiveWordDTO
    ) throws URISyntaxException {
        LOG.debug("REST请求更新敏感词 : {}, {}", id, sensitiveWordDTO);
        if (sensitiveWordDTO.getId() == null) {
            throw new BadRequestAlertException("ID无效", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, sensitiveWordDTO.getId())) {
            throw new BadRequestAlertException("ID不匹配", ENTITY_NAME, "idinvalid");
        }

        if (!sensitiveWordRepository.existsById(id)) {
            throw new BadRequestAlertException("实体不存在", ENTITY_NAME, "idnotfound");
        }

        sensitiveWordDTO = sensitiveWordService.update(sensitiveWordDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, sensitiveWordDTO.getId().toString()))
            .body(sensitiveWordDTO);
    }

    /**
     * {@code PATCH  /sensitive-words/:id} : 部分更新敏感词的给定字段，如果字段为null则忽略
     *
     * @param id 要保存的敏感词DTO的ID
     * @param sensitiveWordDTO 要更新的敏感词DTO
     * @return {@link ResponseEntity} 状态码 {@code 200 (OK)} 并在响应体中包含更新后的敏感词DTO,
     *         如果敏感词DTO无效则返回状态码 {@code 400 (Bad Request)},
     *         如果敏感词DTO未找到则返回状态码 {@code 404 (Not Found)},
     *         如果敏感词DTO无法更新则返回状态码 {@code 500 (Internal Server Error)}
     * @throws URISyntaxException 如果位置URI语法不正确
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<SensitiveWordDTO> partialUpdateSensitiveWord(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody SensitiveWordDTO sensitiveWordDTO
    ) throws URISyntaxException {
        LOG.debug("REST请求部分更新敏感词 : {}, {}", id, sensitiveWordDTO);
        if (sensitiveWordDTO.getId() == null) {
            throw new BadRequestAlertException("ID无效", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, sensitiveWordDTO.getId())) {
            throw new BadRequestAlertException("ID不匹配", ENTITY_NAME, "idinvalid");
        }

        if (!sensitiveWordRepository.existsById(id)) {
            throw new BadRequestAlertException("实体不存在", ENTITY_NAME, "idnotfound");
        }

        Optional<SensitiveWordDTO> result = sensitiveWordService.partialUpdate(sensitiveWordDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, sensitiveWordDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /sensitive-words} : 获取所有敏感词。
     *
     * @param pageable 分页信息
     * @return {@link ResponseEntity} 状态码 {@code 200 (OK)} 并在响应体中包含敏感词列表
     */
    @GetMapping("")
    public ResponseEntity<List<SensitiveWordDTO>> getAllSensitiveWords(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST请求获取敏感词分页数据");
        Page<SensitiveWordDTO> page = sensitiveWordService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /sensitive-words/:id} : 根据"id"获取敏感词。
     *
     * @param id 要检索的敏感词DTO的ID
     * @return {@link ResponseEntity} 状态码 {@code 200 (OK)} 并在响应体中包含敏感词DTO，
     *         如果未找到则返回状态码 {@code 404 (Not Found)}
     */
    @GetMapping("/{id}")
    public ResponseEntity<SensitiveWordDTO> getSensitiveWord(@PathVariable("id") Long id) {
        LOG.debug("REST请求获取敏感词 : {}", id);
        Optional<SensitiveWordDTO> sensitiveWordDTO = sensitiveWordService.findOne(id);
        return ResponseUtil.wrapOrNotFound(sensitiveWordDTO);
    }

    /**
     * {@code DELETE  /sensitive-words/:id} : 删除"id"敏感词。
     *
     * @param id 要删除的敏感词DTO的ID
     * @return {@link ResponseEntity} 状态码 {@code 204 (NO_CONTENT)}
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteSensitiveWord(@PathVariable("id") Long id) {
        LOG.debug("REST请求删除敏感词 : {}", id);
        sensitiveWordService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
