package com.whiskerguard.general.service;

import com.whiskerguard.general.model.EmailRequest;
import com.whiskerguard.general.model.NotificationResponse;
import com.whiskerguard.general.model.PushRequest;
import com.whiskerguard.general.model.SmsRequest;

/**
 * 通知服务接口
 */
public interface NotificationService {
    /**
     * 发送短信通知
     *
     * @param request 短信请求
     * @return 通知响应
     */
    NotificationResponse sendSms(SmsRequest request);

    /**
     * 发送邮件通知
     *
     * @param request 邮件请求
     * @return 通知响应
     */
    NotificationResponse sendEmail(EmailRequest request);

    /**
     * 发送APP推送通知
     *
     * @param request 推送请求
     * @return 通知响应
     */
    NotificationResponse sendPush(PushRequest request);
}
