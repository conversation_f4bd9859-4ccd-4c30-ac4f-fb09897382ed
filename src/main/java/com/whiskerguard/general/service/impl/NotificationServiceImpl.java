package com.whiskerguard.general.service.impl;

import com.whiskerguard.general.config.ApplicationProperties;
import com.whiskerguard.general.model.*;
import com.whiskerguard.general.service.EmailService;
import com.whiskerguard.general.service.NotificationService;
import com.whiskerguard.general.service.PushService;
import com.whiskerguard.general.service.SmsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * 通知服务实现
 */
@Service
public class NotificationServiceImpl implements NotificationService {

    private static final Logger log = LoggerFactory.getLogger(NotificationServiceImpl.class);

    private final ApplicationProperties applicationProperties;
    private final EmailService emailService;
    private final PushService pushService;

    private final SmsService aliyunSmsService;
    private final SmsService tencentSmsService;

    @Autowired
    public NotificationServiceImpl(
        ApplicationProperties applicationProperties,
        EmailService emailService,
        PushService pushService,
        @Qualifier("aliyunSmsService") SmsService aliyunSmsService,
        @Qualifier("tencentSmsService") SmsService tencentSmsService
    ) {
        this.applicationProperties = applicationProperties;
        this.emailService = emailService;
        this.pushService = pushService;
        this.aliyunSmsService = aliyunSmsService;
        this.tencentSmsService = tencentSmsService;
    }

    @Override
    public NotificationResponse sendSms(SmsRequest request) {
        log.debug("发送短信通知: {}", request.getRecipient());

        // 根据配置或请求选择短信服务提供商
        SmsProviderType providerType = request.getProviderType();
        if (providerType == null) {
            providerType = applicationProperties.getNotification().getSms().getDefaultProvider();
        }

        // 根据提供商类型选择对应的服务
        switch (providerType) {
            case ALIYUN:
                return aliyunSmsService.send(request);
            case TENCENT:
                return tencentSmsService.send(request);
            default:
                return NotificationResponse.failure("不支持的短信服务提供商: " + providerType);
        }
    }

    @Override
    public NotificationResponse sendEmail(EmailRequest request) {
        log.debug("发送邮件通知: {}", request.getRecipient());
        return emailService.send(request);
    }

    @Override
    public NotificationResponse sendPush(PushRequest request) {
        log.debug("发送推送通知: {}", request.getTargets());
        return pushService.send(request);
    }
}
