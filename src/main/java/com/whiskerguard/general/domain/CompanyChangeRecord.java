package com.whiskerguard.general.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * Company change record entity.
 * Stores change records from Tianyancha API.
 *
 * 企业变更记录实体。
 * 存储从天眼查API获取的变更记录。
 */
@Entity
@Table(name = "company_change_record")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class CompanyChangeRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Primary key ID
     *
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * Item that changed
     *
     * 变更项目
     */
    @Size(max = 200)
    @Column(name = "change_item", length = 200)
    private String changeItem;

    /**
     * Content before change
     *
     * 变更前内容
     */
    @Lob
    @Column(name = "before_content")
    private String beforeContent;

    /**
     * Content after change
     *
     * 变更后内容
     */
    @Lob
    @Column(name = "after_content")
    private String afterContent;

    /**
     * Date of change
     *
     * 变更日期
     */
    @Column(name = "change_date")
    private Instant changeDate;

    /**
     * Additional change details as JSON
     *
     * 额外的变更详情，以JSON形式存储
     */
    @Lob
    @Column(name = "additional_details")
    private String additionalDetails;

    /**
     * Relationship with Company entity
     *
     * 与企业实体的关联关系
     */
    @ManyToOne
    @JsonIgnoreProperties(value = { "contact", "risks", "changeRecords", "dishonestPersons", "caseFilings" }, allowSetters = true)
    private Company company;

    // Constructors - 构造函数

    public CompanyChangeRecord() {}

    // Getters and Setters - getter和setter方法

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public CompanyChangeRecord id(Long id) {
        this.setId(id);
        return this;
    }

    public String getChangeItem() {
        return changeItem;
    }

    public void setChangeItem(String changeItem) {
        this.changeItem = changeItem;
    }

    public CompanyChangeRecord changeItem(String changeItem) {
        this.setChangeItem(changeItem);
        return this;
    }

    public String getBeforeContent() {
        return beforeContent;
    }

    public void setBeforeContent(String beforeContent) {
        this.beforeContent = beforeContent;
    }

    public CompanyChangeRecord beforeContent(String beforeContent) {
        this.setBeforeContent(beforeContent);
        return this;
    }

    public String getAfterContent() {
        return afterContent;
    }

    public void setAfterContent(String afterContent) {
        this.afterContent = afterContent;
    }

    public CompanyChangeRecord afterContent(String afterContent) {
        this.setAfterContent(afterContent);
        return this;
    }

    public Instant getChangeDate() {
        return changeDate;
    }

    public void setChangeDate(Instant changeDate) {
        this.changeDate = changeDate;
    }

    public CompanyChangeRecord changeDate(Instant changeDate) {
        this.setChangeDate(changeDate);
        return this;
    }

    public String getAdditionalDetails() {
        return additionalDetails;
    }

    public void setAdditionalDetails(String additionalDetails) {
        this.additionalDetails = additionalDetails;
    }

    public CompanyChangeRecord additionalDetails(String additionalDetails) {
        this.setAdditionalDetails(additionalDetails);
        return this;
    }

    public Company getCompany() {
        return company;
    }

    public void setCompany(Company company) {
        this.company = company;
    }

    public CompanyChangeRecord company(Company company) {
        this.setCompany(company);
        return this;
    }

    // equals, hashCode, toString methods - equals、hashCode和toString方法

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof CompanyChangeRecord)) {
            return false;
        }
        return id != null && id.equals(((CompanyChangeRecord) o).id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return (
            "CompanyChangeRecord{" +
            "id=" +
            getId() +
            ", changeItem='" +
            getChangeItem() +
            "'" +
            ", beforeContent='" +
            getBeforeContent() +
            "'" +
            ", afterContent='" +
            getAfterContent() +
            "'" +
            ", changeDate='" +
            getChangeDate() +
            "'" +
            "}"
        );
    }
}
