package com.whiskerguard.general.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * Company risk information entity.
 * Stores risk data from Tianyancha API.
 *
 * 企业风险信息实体。
 * 存储从天眼查API获取的风险数据。
 */
@Entity
@Table(name = "company_risk")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class CompanyRisk implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Primary key ID
     *
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * Type of risk (e.g., Legal, Operational, Financial)
     *
     * 风险类型（如：法律风险、运营风险、财务风险）
     */
    @Size(max = 100)
    @Column(name = "risk_type", length = 100)
    private String riskType;

    /**
     * Description of the risk
     *
     * 风险描述
     */
    @Size(max = 1000)
    @Column(name = "risk_description", length = 1000)
    private String riskDescription;

    /**
     * Date of the risk event
     *
     * 风险事件日期
     */
    @Column(name = "risk_date")
    private Instant riskDate;

    /**
     * Risk level or severity
     *
     * 风险等级或严重程度
     */
    @Size(max = 50)
    @Column(name = "risk_level", length = 50)
    private String riskLevel;

    /**
     * Full risk details as JSON string
     *
     * 完整的风险详情，以JSON字符串形式存储
     */
    @Lob
    @Column(name = "details")
    private String details;

    /**
     * Relationship with Company entity
     *
     * 与企业实体的关联关系
     */
    @ManyToOne
    @JsonIgnoreProperties(value = { "contact", "risks", "changeRecords", "dishonestPersons", "caseFilings" }, allowSetters = true)
    private Company company;

    // Constructors - 构造函数

    public CompanyRisk() {}

    // Getters and Setters - getter和setter方法

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public CompanyRisk id(Long id) {
        this.setId(id);
        return this;
    }

    public String getRiskType() {
        return riskType;
    }

    public void setRiskType(String riskType) {
        this.riskType = riskType;
    }

    public CompanyRisk riskType(String riskType) {
        this.setRiskType(riskType);
        return this;
    }

    public String getRiskDescription() {
        return riskDescription;
    }

    public void setRiskDescription(String riskDescription) {
        this.riskDescription = riskDescription;
    }

    public CompanyRisk riskDescription(String riskDescription) {
        this.setRiskDescription(riskDescription);
        return this;
    }

    public Instant getRiskDate() {
        return riskDate;
    }

    public void setRiskDate(Instant riskDate) {
        this.riskDate = riskDate;
    }

    public CompanyRisk riskDate(Instant riskDate) {
        this.setRiskDate(riskDate);
        return this;
    }

    public String getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }

    public CompanyRisk riskLevel(String riskLevel) {
        this.setRiskLevel(riskLevel);
        return this;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public CompanyRisk details(String details) {
        this.setDetails(details);
        return this;
    }

    public Company getCompany() {
        return company;
    }

    public void setCompany(Company company) {
        this.company = company;
    }

    public CompanyRisk company(Company company) {
        this.setCompany(company);
        return this;
    }

    // equals, hashCode, toString methods - equals、hashCode和toString方法

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof CompanyRisk)) {
            return false;
        }
        return id != null && id.equals(((CompanyRisk) o).id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return (
            "CompanyRisk{" +
            "id=" +
            getId() +
            ", riskType='" +
            getRiskType() +
            "'" +
            ", riskDescription='" +
            getRiskDescription() +
            "'" +
            ", riskDate='" +
            getRiskDate() +
            "'" +
            ", riskLevel='" +
            getRiskLevel() +
            "'" +
            "}"
        );
    }
}
