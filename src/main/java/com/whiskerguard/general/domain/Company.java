package com.whiskerguard.general.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.HashSet;
import java.util.Set;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * Company entity for storing basic company information from Tianyancha API.
 * This entity serves as the central point for all company-related data.
 *
 * 公司实体，用于存储从天眼查API获取的基本公司信息。
 * 该实体作为所有公司相关数据的中心点。
 */
@Entity
@Table(name = "company")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class Company implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * Tianyancha company ID (from API response)
     * 天眼查公司ID（来自API响应）
     */
    @Column(name = "tianyancha_id")
    private Long tianyanchaId;

    /**
     * Company name
     * 公司名称
     */
    @NotNull
    @Size(max = 500)
    @Column(name = "name", length = 500, nullable = false)
    private String name;

    /**
     * Unified Social Credit Code
     * 统一社会信用代码
     */
    @Size(max = 50)
    @Column(name = "unified_social_credit_code", length = 50)
    private String unifiedSocialCreditCode;

    /**
     * Legal person name
     * 法人姓名
     */
    @Size(max = 200)
    @Column(name = "legal_person_name", length = 200)
    private String legalPersonName;

    /**
     * Registration status (e.g., "存续", "注销")
     * 注册状态（例如：“存续”、“注销”）
     */
    @Size(max = 50)
    @Column(name = "reg_status", length = 50)
    private String regStatus;

    /**
     * Registered capital
     * 注册资本
     */
    @Size(max = 100)
    @Column(name = "reg_capital", length = 100)
    private String regCapital;

    /**
     * Registered capital currency
     * 注册资本币种
     */
    @Size(max = 20)
    @Column(name = "reg_capital_currency", length = 20)
    private String regCapitalCurrency;

    /**
     * Establishment time
     * 成立时间
     */
    @Column(name = "establish_time")
    private Instant establishTime;

    /**
     * Company organization type
     * 公司组织类型
     */
    @Size(max = 200)
    @Column(name = "company_org_type", length = 200)
    private String companyOrgType;

    /**
     * Registration number
     * 注册号
     */
    @Size(max = 50)
    @Column(name = "reg_number", length = 50)
    private String regNumber;

    /**
     * Tax number
     * 税号
     */
    @Size(max = 50)
    @Column(name = "tax_number", length = 50)
    private String taxNumber;

    /**
     * Organization number
     * 组织机构代码
     */
    @Size(max = 50)
    @Column(name = "org_number", length = 50)
    private String orgNumber;

    /**
     * Industry
     * 行业
     */
    @Size(max = 200)
    @Column(name = "industry", length = 200)
    private String industry;

    /**
     * Registration location
     * 注册地点
     */
    @Size(max = 1000)
    @Column(name = "reg_location", length = 1000)
    private String regLocation;

    /**
     * Business scope
     * 经营范围
     */
    @Lob
    @Column(name = "business_scope")
    private String businessScope;

    /**
     * Last update time from Tianyancha
     * 最后一次从天眼查更新的时间
     */
    @Column(name = "tianyancha_update_time")
    private Instant tianyanchaUpdateTime;

    /**
     * Local cache time (when this record was last updated in our database)
     * 本地缓存时间（此记录上次在我们数据库中更新的时间）
     */
    @Column(name = "cache_time")
    private Instant cacheTime;

    /**
     * History names (comma-separated)
     * 历史名称（以逗号分隔）
     */
    @Size(max = 1000)
    @Column(name = "history_names", length = 1000)
    private String historyNames;

    /**
     * Cancel date
     * 注销日期
     */
    @Column(name = "cancel_date")
    private Instant cancelDate;

    /**
     * Revoke date
     * 吊销日期
     */
    @Column(name = "revoke_date")
    private Instant revokeDate;

    /**
     * Revoke reason
     * 吊销原因
     */
    @Size(max = 500)
    @Column(name = "revoke_reason", length = 500)
    private String revokeReason;

    /**
     * Cancel reason
     * 注销原因
     */
    @Size(max = 500)
    @Column(name = "cancel_reason", length = 500)
    private String cancelReason;

    /**
     * Approved time
     * 批准时间
     */
    @Column(name = "approved_time")
    private Instant approvedTime;

    /**
     * From time
     * 起始时间
     */
    @Column(name = "from_time")
    private Instant fromTime;

    /**
     * To time
     * 结束时间
     */
    @Column(name = "to_time")
    private Instant toTime;

    /**
     * Actual capital
     * 实际资本
     */
    @Size(max = 100)
    @Column(name = "actual_capital", length = 100)
    private String actualCapital;

    /**
     * Actual capital currency
     * 实际资本币种
     */
    @Size(max = 20)
    @Column(name = "actual_capital_currency", length = 20)
    private String actualCapitalCurrency;

    /**
     * Registration institute
     * 登记机关
     */
    @Size(max = 500)
    @Column(name = "reg_institute", length = 500)
    private String regInstitute;

    /**
     * City
     * 城市
     */
    @Size(max = 100)
    @Column(name = "city", length = 100)
    private String city;

    /**
     * District
     * 区/县
     */
    @Size(max = 100)
    @Column(name = "district", length = 100)
    private String district;

    /**
     * Staff number range
     * 员工人数范围
     */
    @Size(max = 50)
    @Column(name = "staff_num_range", length = 50)
    private String staffNumRange;

    /**
     * Social staff number
     * 社会统一信用代码
     */
    @Column(name = "social_staff_num")
    private Integer socialStaffNum;

    /**
     * Bond number
     * 债券编号
     */
    @Size(max = 50)
    @Column(name = "bond_num", length = 50)
    private String bondNum;

    /**
     * Bond name
     * 债券名称
     */
    @Size(max = 200)
    @Column(name = "bond_name", length = 200)
    private String bondName;

    /**
     * Bond type
     * 债券类型
     */
    @Size(max = 50)
    @Column(name = "bond_type", length = 50)
    private String bondType;

    /**
     * Used bond name
     * 使用的债券名称
     */
    @Size(max = 200)
    @Column(name = "used_bond_name", length = 200)
    private String usedBondName;

    /**
     * Company alias
     * 公司别名
     */
    @Size(max = 500)
    @Column(name = "alias", length = 500)
    private String alias;

    /**
     * Property3 field from API
     * API中的属性3字段
     */
    @Size(max = 500)
    @Column(name = "property3", length = 500)
    private String property3;

    /**
     * Tags (comma-separated)
     * 标签（以逗号分隔）
     */
    @Size(max = 1000)
    @Column(name = "tags", length = 1000)
    private String tags;

    /**
     * Percentile score
     * 百分位得分
     */
    @Column(name = "percentile_score")
    private Integer percentileScore;

    /**
     * Is micro enterprise (0 or 1)
     * 是否为微型企业（0或1）
     */
    @Column(name = "is_micro_ent")
    private Integer isMicroEnt;

    /**
     * Base field from API
     * API中的基础字段
     */
    @Size(max = 50)
    @Column(name = "base", length = 50)
    private String base;

    /**
     * Type field from API
     * API中的类型字段
     */
    @Column(name = "type")
    private Integer type;

    /**
     * Company form
     * 公司形态
     */
    @Size(max = 200)
    @Column(name = "comp_form", length = 200)
    private String compForm;

    /**
     * Industry category
     * 行业类别
     */
    @Size(max = 100)
    @Column(name = "industry_category", length = 100)
    private String industryCategory;

    /**
     * Industry category big
     * 行业大类
     */
    @Size(max = 100)
    @Column(name = "industry_category_big", length = 100)
    private String industryCategoryBig;

    /**
     * Industry category middle
     * 行业中类
     */
    @Size(max = 100)
    @Column(name = "industry_category_middle", length = 100)
    private String industryCategoryMiddle;

    /**
     * Industry category small
     * 行业小类
     */
    @Size(max = 100)
    @Column(name = "industry_category_small", length = 100)
    private String industryCategorySmall;

    // Relationships

    @OneToOne(mappedBy = "company", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "company" }, allowSetters = true)
    private CompanyContact contact;

    @OneToMany(mappedBy = "company", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "company" }, allowSetters = true)
    private Set<CompanyRisk> risks = new HashSet<>();

    @OneToMany(mappedBy = "company", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "company" }, allowSetters = true)
    private Set<CompanyChangeRecord> changeRecords = new HashSet<>();

    @OneToMany(mappedBy = "company", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "company" }, allowSetters = true)
    private Set<CompanyDishonestPerson> dishonestPersons = new HashSet<>();

    @OneToMany(mappedBy = "company", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "company" }, allowSetters = true)
    private Set<CompanyCaseFiling> caseFilings = new HashSet<>();

    // Constructors

    public Company() {}

    public Company(String name) {
        this.name = name;
        this.cacheTime = Instant.now();
    }

    // Getters and Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Company id(Long id) {
        this.setId(id);
        return this;
    }

    public Long getTianyanchaId() {
        return tianyanchaId;
    }

    public void setTianyanchaId(Long tianyanchaId) {
        this.tianyanchaId = tianyanchaId;
    }

    public Company tianyanchaId(Long tianyanchaId) {
        this.setTianyanchaId(tianyanchaId);
        return this;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Company name(String name) {
        this.setName(name);
        return this;
    }

    public String getUnifiedSocialCreditCode() {
        return unifiedSocialCreditCode;
    }

    public void setUnifiedSocialCreditCode(String unifiedSocialCreditCode) {
        this.unifiedSocialCreditCode = unifiedSocialCreditCode;
    }

    public Company unifiedSocialCreditCode(String unifiedSocialCreditCode) {
        this.setUnifiedSocialCreditCode(unifiedSocialCreditCode);
        return this;
    }

    public String getLegalPersonName() {
        return legalPersonName;
    }

    public void setLegalPersonName(String legalPersonName) {
        this.legalPersonName = legalPersonName;
    }

    public Company legalPersonName(String legalPersonName) {
        this.setLegalPersonName(legalPersonName);
        return this;
    }

    public String getRegStatus() {
        return regStatus;
    }

    public void setRegStatus(String regStatus) {
        this.regStatus = regStatus;
    }

    public Company regStatus(String regStatus) {
        this.setRegStatus(regStatus);
        return this;
    }

    public String getRegCapital() {
        return regCapital;
    }

    public void setRegCapital(String regCapital) {
        this.regCapital = regCapital;
    }

    public Company regCapital(String regCapital) {
        this.setRegCapital(regCapital);
        return this;
    }

    public String getRegCapitalCurrency() {
        return regCapitalCurrency;
    }

    public void setRegCapitalCurrency(String regCapitalCurrency) {
        this.regCapitalCurrency = regCapitalCurrency;
    }

    public Company regCapitalCurrency(String regCapitalCurrency) {
        this.setRegCapitalCurrency(regCapitalCurrency);
        return this;
    }

    public Instant getEstablishTime() {
        return establishTime;
    }

    public void setEstablishTime(Instant establishTime) {
        this.establishTime = establishTime;
    }

    public Company establishTime(Instant establishTime) {
        this.setEstablishTime(establishTime);
        return this;
    }

    public String getCompanyOrgType() {
        return companyOrgType;
    }

    public void setCompanyOrgType(String companyOrgType) {
        this.companyOrgType = companyOrgType;
    }

    public Company companyOrgType(String companyOrgType) {
        this.setCompanyOrgType(companyOrgType);
        return this;
    }

    public String getRegNumber() {
        return regNumber;
    }

    public void setRegNumber(String regNumber) {
        this.regNumber = regNumber;
    }

    public Company regNumber(String regNumber) {
        this.setRegNumber(regNumber);
        return this;
    }

    public String getTaxNumber() {
        return taxNumber;
    }

    public void setTaxNumber(String taxNumber) {
        this.taxNumber = taxNumber;
    }

    public Company taxNumber(String taxNumber) {
        this.setTaxNumber(taxNumber);
        return this;
    }

    public String getOrgNumber() {
        return orgNumber;
    }

    public void setOrgNumber(String orgNumber) {
        this.orgNumber = orgNumber;
    }

    public Company orgNumber(String orgNumber) {
        this.setOrgNumber(orgNumber);
        return this;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public Company industry(String industry) {
        this.setIndustry(industry);
        return this;
    }

    public String getRegLocation() {
        return regLocation;
    }

    public void setRegLocation(String regLocation) {
        this.regLocation = regLocation;
    }

    public Company regLocation(String regLocation) {
        this.setRegLocation(regLocation);
        return this;
    }

    public String getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public Company businessScope(String businessScope) {
        this.setBusinessScope(businessScope);
        return this;
    }

    public Instant getTianyanchaUpdateTime() {
        return tianyanchaUpdateTime;
    }

    public void setTianyanchaUpdateTime(Instant tianyanchaUpdateTime) {
        this.tianyanchaUpdateTime = tianyanchaUpdateTime;
    }

    public Company tianyanchaUpdateTime(Instant tianyanchaUpdateTime) {
        this.setTianyanchaUpdateTime(tianyanchaUpdateTime);
        return this;
    }

    public Instant getCacheTime() {
        return cacheTime;
    }

    public void setCacheTime(Instant cacheTime) {
        this.cacheTime = cacheTime;
    }

    public Company cacheTime(Instant cacheTime) {
        this.setCacheTime(cacheTime);
        return this;
    }

    // Additional getters and setters for all other fields...
    // (I'll include a few more key ones for brevity)

    public String getHistoryNames() {
        return historyNames;
    }

    public void setHistoryNames(String historyNames) {
        this.historyNames = historyNames;
    }

    public Company historyNames(String historyNames) {
        this.setHistoryNames(historyNames);
        return this;
    }

    public Instant getCancelDate() {
        return cancelDate;
    }

    public void setCancelDate(Instant cancelDate) {
        this.cancelDate = cancelDate;
    }

    public Company cancelDate(Instant cancelDate) {
        this.setCancelDate(cancelDate);
        return this;
    }

    public Instant getRevokeDate() {
        return revokeDate;
    }

    public void setRevokeDate(Instant revokeDate) {
        this.revokeDate = revokeDate;
    }

    public Company revokeDate(Instant revokeDate) {
        this.setRevokeDate(revokeDate);
        return this;
    }

    public String getRevokeReason() {
        return revokeReason;
    }

    public void setRevokeReason(String revokeReason) {
        this.revokeReason = revokeReason;
    }

    public Company revokeReason(String revokeReason) {
        this.setRevokeReason(revokeReason);
        return this;
    }

    public String getCancelReason() {
        return cancelReason;
    }

    public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
    }

    public Company cancelReason(String cancelReason) {
        this.setCancelReason(cancelReason);
        return this;
    }

    public Instant getApprovedTime() {
        return approvedTime;
    }

    public void setApprovedTime(Instant approvedTime) {
        this.approvedTime = approvedTime;
    }

    public Company approvedTime(Instant approvedTime) {
        this.setApprovedTime(approvedTime);
        return this;
    }

    public Instant getFromTime() {
        return fromTime;
    }

    public void setFromTime(Instant fromTime) {
        this.fromTime = fromTime;
    }

    public Company fromTime(Instant fromTime) {
        this.setFromTime(fromTime);
        return this;
    }

    public Instant getToTime() {
        return toTime;
    }

    public void setToTime(Instant toTime) {
        this.toTime = toTime;
    }

    public Company toTime(Instant toTime) {
        this.setToTime(toTime);
        return this;
    }

    public String getActualCapital() {
        return actualCapital;
    }

    public void setActualCapital(String actualCapital) {
        this.actualCapital = actualCapital;
    }

    public Company actualCapital(String actualCapital) {
        this.setActualCapital(actualCapital);
        return this;
    }

    public String getActualCapitalCurrency() {
        return actualCapitalCurrency;
    }

    public void setActualCapitalCurrency(String actualCapitalCurrency) {
        this.actualCapitalCurrency = actualCapitalCurrency;
    }

    public Company actualCapitalCurrency(String actualCapitalCurrency) {
        this.setActualCapitalCurrency(actualCapitalCurrency);
        return this;
    }

    public String getRegInstitute() {
        return regInstitute;
    }

    public void setRegInstitute(String regInstitute) {
        this.regInstitute = regInstitute;
    }

    public Company regInstitute(String regInstitute) {
        this.setRegInstitute(regInstitute);
        return this;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public Company city(String city) {
        this.setCity(city);
        return this;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public Company district(String district) {
        this.setDistrict(district);
        return this;
    }

    public String getStaffNumRange() {
        return staffNumRange;
    }

    public void setStaffNumRange(String staffNumRange) {
        this.staffNumRange = staffNumRange;
    }

    public Company staffNumRange(String staffNumRange) {
        this.setStaffNumRange(staffNumRange);
        return this;
    }

    public Integer getSocialStaffNum() {
        return socialStaffNum;
    }

    public void setSocialStaffNum(Integer socialStaffNum) {
        this.socialStaffNum = socialStaffNum;
    }

    public Company socialStaffNum(Integer socialStaffNum) {
        this.setSocialStaffNum(socialStaffNum);
        return this;
    }

    public String getBondNum() {
        return bondNum;
    }

    public void setBondNum(String bondNum) {
        this.bondNum = bondNum;
    }

    public Company bondNum(String bondNum) {
        this.setBondNum(bondNum);
        return this;
    }

    public String getBondName() {
        return bondName;
    }

    public void setBondName(String bondName) {
        this.bondName = bondName;
    }

    public Company bondName(String bondName) {
        this.setBondName(bondName);
        return this;
    }

    public String getBondType() {
        return bondType;
    }

    public void setBondType(String bondType) {
        this.bondType = bondType;
    }

    public Company bondType(String bondType) {
        this.setBondType(bondType);
        return this;
    }

    public String getUsedBondName() {
        return usedBondName;
    }

    public void setUsedBondName(String usedBondName) {
        this.usedBondName = usedBondName;
    }

    public Company usedBondName(String usedBondName) {
        this.setUsedBondName(usedBondName);
        return this;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public Company alias(String alias) {
        this.setAlias(alias);
        return this;
    }

    public String getProperty3() {
        return property3;
    }

    public void setProperty3(String property3) {
        this.property3 = property3;
    }

    public Company property3(String property3) {
        this.setProperty3(property3);
        return this;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public Company tags(String tags) {
        this.setTags(tags);
        return this;
    }

    public Integer getPercentileScore() {
        return percentileScore;
    }

    public void setPercentileScore(Integer percentileScore) {
        this.percentileScore = percentileScore;
    }

    public Company percentileScore(Integer percentileScore) {
        this.setPercentileScore(percentileScore);
        return this;
    }

    public Integer getIsMicroEnt() {
        return isMicroEnt;
    }

    public void setIsMicroEnt(Integer isMicroEnt) {
        this.isMicroEnt = isMicroEnt;
    }

    public Company isMicroEnt(Integer isMicroEnt) {
        this.setIsMicroEnt(isMicroEnt);
        return this;
    }

    public String getBase() {
        return base;
    }

    public void setBase(String base) {
        this.base = base;
    }

    public Company base(String base) {
        this.setBase(base);
        return this;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Company type(Integer type) {
        this.setType(type);
        return this;
    }

    public String getCompForm() {
        return compForm;
    }

    public void setCompForm(String compForm) {
        this.compForm = compForm;
    }

    public Company compForm(String compForm) {
        this.setCompForm(compForm);
        return this;
    }

    public String getIndustryCategory() {
        return industryCategory;
    }

    public void setIndustryCategory(String industryCategory) {
        this.industryCategory = industryCategory;
    }

    public Company industryCategory(String industryCategory) {
        this.setIndustryCategory(industryCategory);
        return this;
    }

    public String getIndustryCategoryBig() {
        return industryCategoryBig;
    }

    public void setIndustryCategoryBig(String industryCategoryBig) {
        this.industryCategoryBig = industryCategoryBig;
    }

    public Company industryCategoryBig(String industryCategoryBig) {
        this.setIndustryCategoryBig(industryCategoryBig);
        return this;
    }

    public String getIndustryCategoryMiddle() {
        return industryCategoryMiddle;
    }

    public void setIndustryCategoryMiddle(String industryCategoryMiddle) {
        this.industryCategoryMiddle = industryCategoryMiddle;
    }

    public Company industryCategoryMiddle(String industryCategoryMiddle) {
        this.setIndustryCategoryMiddle(industryCategoryMiddle);
        return this;
    }

    public String getIndustryCategorySmall() {
        return industryCategorySmall;
    }

    public void setIndustryCategorySmall(String industryCategorySmall) {
        this.industryCategorySmall = industryCategorySmall;
    }

    public Company industryCategorySmall(String industryCategorySmall) {
        this.setIndustryCategorySmall(industryCategorySmall);
        return this;
    }

    // Relationship getters and setters

    public CompanyContact getContact() {
        return contact;
    }

    public void setContact(CompanyContact contact) {
        if (this.contact != null) {
            this.contact.setCompany(null);
        }
        if (contact != null) {
            contact.setCompany(this);
        }
        this.contact = contact;
    }

    public Company contact(CompanyContact contact) {
        this.setContact(contact);
        return this;
    }

    public Set<CompanyRisk> getRisks() {
        return risks;
    }

    public void setRisks(Set<CompanyRisk> risks) {
        if (this.risks != null) {
            this.risks.forEach(i -> i.setCompany(null));
        }
        if (risks != null) {
            risks.forEach(i -> i.setCompany(this));
        }
        this.risks = risks;
    }

    public Company risks(Set<CompanyRisk> risks) {
        this.setRisks(risks);
        return this;
    }

    public Company addRisk(CompanyRisk risk) {
        this.risks.add(risk);
        risk.setCompany(this);
        return this;
    }

    public Company removeRisk(CompanyRisk risk) {
        this.risks.remove(risk);
        risk.setCompany(null);
        return this;
    }

    public Set<CompanyChangeRecord> getChangeRecords() {
        return changeRecords;
    }

    public void setChangeRecords(Set<CompanyChangeRecord> changeRecords) {
        if (this.changeRecords != null) {
            this.changeRecords.forEach(i -> i.setCompany(null));
        }
        if (changeRecords != null) {
            changeRecords.forEach(i -> i.setCompany(this));
        }
        this.changeRecords = changeRecords;
    }

    public Company changeRecords(Set<CompanyChangeRecord> changeRecords) {
        this.setChangeRecords(changeRecords);
        return this;
    }

    public Company addChangeRecord(CompanyChangeRecord changeRecord) {
        this.changeRecords.add(changeRecord);
        changeRecord.setCompany(this);
        return this;
    }

    public Company removeChangeRecord(CompanyChangeRecord changeRecord) {
        this.changeRecords.remove(changeRecord);
        changeRecord.setCompany(null);
        return this;
    }

    public Set<CompanyDishonestPerson> getDishonestPersons() {
        return dishonestPersons;
    }

    public void setDishonestPersons(Set<CompanyDishonestPerson> dishonestPersons) {
        if (this.dishonestPersons != null) {
            this.dishonestPersons.forEach(i -> i.setCompany(null));
        }
        if (dishonestPersons != null) {
            dishonestPersons.forEach(i -> i.setCompany(this));
        }
        this.dishonestPersons = dishonestPersons;
    }

    public Company dishonestPersons(Set<CompanyDishonestPerson> dishonestPersons) {
        this.setDishonestPersons(dishonestPersons);
        return this;
    }

    public Company addDishonestPerson(CompanyDishonestPerson dishonestPerson) {
        this.dishonestPersons.add(dishonestPerson);
        dishonestPerson.setCompany(this);
        return this;
    }

    public Company removeDishonestPerson(CompanyDishonestPerson dishonestPerson) {
        this.dishonestPersons.remove(dishonestPerson);
        dishonestPerson.setCompany(null);
        return this;
    }

    public Set<CompanyCaseFiling> getCaseFilings() {
        return caseFilings;
    }

    public void setCaseFilings(Set<CompanyCaseFiling> caseFilings) {
        if (this.caseFilings != null) {
            this.caseFilings.forEach(i -> i.setCompany(null));
        }
        if (caseFilings != null) {
            caseFilings.forEach(i -> i.setCompany(this));
        }
        this.caseFilings = caseFilings;
    }

    public Company caseFilings(Set<CompanyCaseFiling> caseFilings) {
        this.setCaseFilings(caseFilings);
        return this;
    }

    public Company addCaseFiling(CompanyCaseFiling caseFiling) {
        this.caseFilings.add(caseFiling);
        caseFiling.setCompany(this);
        return this;
    }

    public Company removeCaseFiling(CompanyCaseFiling caseFiling) {
        this.caseFilings.remove(caseFiling);
        caseFiling.setCompany(null);
        return this;
    }

    // equals and hashCode

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Company)) {
            return false;
        }
        return id != null && id.equals(((Company) o).id);
    }

    @Override
    public int hashCode() {
        // Use id if available, otherwise return 0 for non-persisted entities
        return id != null ? id.hashCode() : 0;
    }

    // toString

    @Override
    public String toString() {
        return (
            "Company{" +
            "id=" +
            getId() +
            ", tianyanchaId=" +
            getTianyanchaId() +
            ", name='" +
            getName() +
            "'" +
            ", unifiedSocialCreditCode='" +
            getUnifiedSocialCreditCode() +
            "'" +
            ", legalPersonName='" +
            getLegalPersonName() +
            "'" +
            ", regStatus='" +
            getRegStatus() +
            "'" +
            ", regCapital='" +
            getRegCapital() +
            "'" +
            ", establishTime='" +
            getEstablishTime() +
            "'" +
            ", companyOrgType='" +
            getCompanyOrgType() +
            "'" +
            ", industry='" +
            getIndustry() +
            "'" +
            ", cacheTime='" +
            getCacheTime() +
            "'" +
            "}"
        );
    }
}
