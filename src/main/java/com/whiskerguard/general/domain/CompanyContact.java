package com.whiskerguard.general.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * Company contact information entity.
 * Stores contact details from Tianyancha API.
 *
 * 企业联系信息实体。
 * 存储从天眼查API获取的联系方式详情。
 */
@Entity
@Table(name = "company_contact")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class CompanyContact implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Primary key ID
     *
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * Phone number
     *
     * 电话号码
     */
    @Size(max = 100)
    @Column(name = "phone_number", length = 100)
    private String phoneNumber;

    /**
     * Email address
     *
     * 电子邮箱地址
     */
    @Size(max = 200)
    @Column(name = "email", length = 200)
    private String email;

    /**
     * Company website
     *
     * 企业网站
     */
    @Size(max = 500)
    @Column(name = "website", length = 500)
    private String website;

    /**
     * Company address
     *
     * 企业地址
     */
    @Size(max = 1000)
    @Column(name = "address", length = 1000)
    private String address;

    /**
     * Additional contact details as JSON
     *
     * 额外的联系方式详情，以JSON形式存储
     */
    @Lob
    @Column(name = "additional_details")
    private String additionalDetails;

    /**
     * Relationship with Company entity (one-to-one)
     *
     * 与企业实体的一对一关联关系
     */
    @OneToOne
    @JoinColumn(unique = true)
    private Company company;

    // Constructors - 构造函数

    public CompanyContact() {}

    // Getters and Setters - getter和setter方法

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public CompanyContact id(Long id) {
        this.setId(id);
        return this;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public CompanyContact phoneNumber(String phoneNumber) {
        this.setPhoneNumber(phoneNumber);
        return this;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public CompanyContact email(String email) {
        this.setEmail(email);
        return this;
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public CompanyContact website(String website) {
        this.setWebsite(website);
        return this;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public CompanyContact address(String address) {
        this.setAddress(address);
        return this;
    }

    public String getAdditionalDetails() {
        return additionalDetails;
    }

    public void setAdditionalDetails(String additionalDetails) {
        this.additionalDetails = additionalDetails;
    }

    public CompanyContact additionalDetails(String additionalDetails) {
        this.setAdditionalDetails(additionalDetails);
        return this;
    }

    public Company getCompany() {
        return company;
    }

    public void setCompany(Company company) {
        this.company = company;
    }

    public CompanyContact company(Company company) {
        this.setCompany(company);
        return this;
    }

    // equals, hashCode, toString methods - equals、hashCode和toString方法

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof CompanyContact)) {
            return false;
        }
        return id != null && id.equals(((CompanyContact) o).id);
    }

    @Override
    public int hashCode() {
        // Use id if available, otherwise return 0 for non-persisted entities
        return id != null ? id.hashCode() : 0;
    }

    @Override
    public String toString() {
        return (
            "CompanyContact{" +
            "id=" +
            getId() +
            ", phoneNumber='" +
            getPhoneNumber() +
            "'" +
            ", email='" +
            getEmail() +
            "'" +
            ", website='" +
            getWebsite() +
            "'" +
            ", address='" +
            getAddress() +
            "'" +
            "}"
        );
    }
}
