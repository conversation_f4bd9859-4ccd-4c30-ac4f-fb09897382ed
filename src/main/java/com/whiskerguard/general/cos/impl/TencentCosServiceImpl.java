/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-common-service
 * 文件名称：null.java
 * 包    名：com.whiskerguard.common.cos.impl
 * 描    述：猫伯伯合规管家公共微服务：提供通用工具类和帮助模块、云对象存储（COS）服务、分布式配置与安全组件、消息和事件驱动服务、缓存与分布式协同、国际化、多语言支持、公共 API 响应与文档功能
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/4/15
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.general.cos.impl;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.http.HttpProtocol;
import com.qcloud.cos.model.*;
import com.qcloud.cos.region.Region;
import com.whiskerguard.general.cos.CosProperties;
import com.whiskerguard.general.cos.CosService;
import java.io.File;
import java.net.URL;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 基于腾讯云 COS 实现的 CosService 接口
 */
@Service
public class TencentCosServiceImpl implements CosService {

    private static final Logger logger = LoggerFactory.getLogger(TencentCosServiceImpl.class);

    @Autowired
    private CosProperties cosProperties;

    private COSClient cosClient;

    /**
     * 初始化 COSClient 对象
     */
    @PostConstruct
    public void init() {
        try {
            BasicCOSCredentials credentials = new BasicCOSCredentials(cosProperties.getSecretId(), cosProperties.getSecretKey());
            ClientConfig clientConfig = new ClientConfig(new Region(cosProperties.getRegion()));
            clientConfig.setHttpProtocol(HttpProtocol.https);
            this.cosClient = new COSClient(credentials, clientConfig);
            logger.info("初始化腾讯云 COSClient 成功，Bucket: {}", cosProperties.getBucketName());
        } catch (Exception e) {
            logger.error("初始化腾讯云 COSClient 失败", e);
            throw new RuntimeException("腾讯云 COSClient 初始化失败", e);
        }
    }

    /**
     * 关闭 COSClient，释放资源
     */
    @PreDestroy
    public void shutdown() {
        if (cosClient != null) {
            cosClient.shutdown();
            logger.info("COSClient 已关闭");
        }
    }

    @Override
    public String upload(File file, String key) throws Exception {
        try {
            PutObjectRequest putObjectRequest = new PutObjectRequest(cosProperties.getBucketName(), key, file);
            PutObjectResult result = cosClient.putObject(putObjectRequest);
            if (result != null) {
                String url = cosProperties.getDomain() + "/" + key;
                logger.info("文件上传成功，URL: {}", url);
                return url;
            } else {
                logger.error("文件上传失败，result 为 null");
                throw new Exception("文件上传失败，返回结果为空");
            }
        } catch (Exception e) {
            logger.error("文件上传失败，key: {}, error: {}", key, e.getMessage());
            throw e;
        }
    }

    @Override
    public String generatePresignedUrl(String key) throws Exception {
        try {
            // 设置 URL 的有效期为 1 小时
            Date expiration = new Date(System.currentTimeMillis() + TimeUnit.HOURS.toMillis(1));
            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(cosProperties.getBucketName(), key);
            request.setMethod(com.qcloud.cos.http.HttpMethodName.GET);
            request.setExpiration(expiration);
            URL url = cosClient.generatePresignedUrl(request);
            if (url != null) {
                logger.info("生成文件临时访问链接成功，URL: {}", url.toString());
                return url.toString();
            } else {
                logger.error("生成文件临时访问链接失败，url 为空");
                throw new Exception("文件临时访问链接生成失败，结果为空");
            }
        } catch (Exception e) {
            logger.error("生成临时访问链接失败，key: {}, error: {}", key, e.getMessage());
            throw e;
        }
    }

    @Override
    public boolean delete(String key) throws Exception {
        try {
            /*DeleteObjectRequest deleteRequest = new DeleteObjectRequest(cosProperties.getBucketName(), key);
            DeleteObjectResult result = cosClient.deleteObject(deleteRequest);
            // 腾讯云 COS SDK 删除成功后不会抛出异常，这里以日志为准
            logger.info("文件删除成功，key: {}", key);
            return true;*/

            DeleteObjectRequest deleteRequest = new DeleteObjectRequest(cosProperties.getBucketName(), key);
            cosClient.deleteObject(deleteRequest);
            // 腾讯云 COS SDK 删除成功后不会抛出异常，这里以日志为准
            logger.info("文件删除成功，key: {}", key);
            return true;
        } catch (Exception e) {
            logger.error("删除文件失败，key: {}, error: {}", key, e.getMessage());
            throw e;
        }
    }

    @Override
    public List<String> listDirectoryFiles(String directoryPath) {
        try {
            // 获取指定目录下的文件列表
            ListObjectsRequest listObjectsRequest = new ListObjectsRequest();
            listObjectsRequest.setBucketName(cosProperties.getBucketName());
            listObjectsRequest.setPrefix(directoryPath);
            ObjectListing objectListing = cosClient.listObjects(listObjectsRequest);
            List<COSObjectSummary> objectSummaries = objectListing.getObjectSummaries();
            // 提取文件名称并返回
            List<String> fileNames = objectSummaries.stream().map(COSObjectSummary::getKey).toList();
            logger.info("获取目录下的文件列表成功，文件数量: {}", fileNames.size());
            return fileNames;
        } catch (Exception e) {
            logger.error("获取目录下的文件列表失败，error: {}", e.getMessage());
        }
        return List.of();
    }
}
