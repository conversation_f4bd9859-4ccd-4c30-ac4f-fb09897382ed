{"generator-jhipster": {"applicationType": "microservice", "authenticationType": "jwt", "baseName": "whiskerguardGeneralService", "buildTool": "maven", "cacheProvider": "redis", "clientFramework": "no", "clientTestFrameworks": null, "clientTheme": null, "creationTimestamp": 1746521503520, "databaseType": "sql", "devDatabaseType": "mysql", "enableHibernateCache": false, "enableTranslation": true, "entities": ["SignatureDocument", "SensitiveWord"], "feignClient": true, "jhipsterVersion": "8.10.0", "jwtSecretKey": "YjY5ZTIzYTIxYWY0ZmVkN2ViYzUxNWUwMTQ5MTQ3ZTMxNTFiMTI0ODJhZGNmZDdiMzhlODRkNTlmYmJkNTk0Mzc1MjEwZmRkYjRiNWM3NzNmNTllMTViOGE0NGVlOWRjMDNhMjkzZTY2OTliZDZjNDVhYjc5NzA4ZmZkMGY2MTM=", "languages": ["zh-cn", "en"], "lastLiquibaseTimestamp": 1749458658000, "microfrontend": null, "microfrontends": [], "nativeLanguage": "zh-cn", "packageName": "com.whiskerguard.general", "prodDatabaseType": "mysql", "reactive": false, "serverPort": "8187", "serviceDiscoveryType": "consul", "skipClient": true, "skipUserManagement": true, "syncUserWithIdp": null, "testFrameworks": [], "withAdminUi": null}}